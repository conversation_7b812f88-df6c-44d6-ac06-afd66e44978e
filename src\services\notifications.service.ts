import type { NotificationResponse } from '@/interfaces/common';
import apiClient from '@/api/axios-instance';

// export function fetchNotifications(): Promise<Notification[]> {
//   return new Promise((resolve) => {
//     setTimeout(() => {
//       resolve(mockNotifications);
//     }, 500);
//   });
// }

export const getNotifications = async (): Promise<NotificationResponse> => {
  const { data } = await apiClient.get<NotificationResponse>('/notifications');
  return data;
};

