<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useDateFormats } from '@/composables/useDateFormats';
import { useLeaveForm } from '@/composables/useLeaveForm';
import { FormMode } from '@/enums';
import { LeaveOfType } from '@/enums/leave';
import { isEmployee } from '@/helpers/staff-helper';
import { cn } from '@/lib/utils';
import { IonPage } from '@ionic/vue';
import { ref } from 'vue';

import {
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NSelect,
  NCheckbox,
} from 'naive-ui';
import { computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';

interface Props {
  leaveId: number;
}

const isSelectGroup = ref(false);
const props = defineProps<Props>();
const route = useRoute();
const { t } = useI18n();

const leaveId = computed(() => props.leaveId || Number(route.params.id));

const {
  form,
  formRef,
  rules,
  relTypeOptions,
  leaveTypeOptions,
  staffManagerOptions,
  staffOptions,
  staffApproveOptions,
  attendanceStatusOptions,
  remainDays,
  totalDaysOff,
  isSubmitDisabled,
  isLoading,
  groupMembers,
  isGroupLeave,
  RelTypeEnum,
  handleSubmit,
  handleCancel,
  populateForm,
  updateMutation,
  user,
} = useLeaveForm(FormMode.UPDATE, leaveId.value);
const { dateFormats } = useDateFormats();

// Computed properties for conditional rendering
const isLeaveType = computed(() => form.value.rel_type === RelTypeEnum.LEAVE);
const isNonEmployee = computed(() => !isEmployee(user?.value?.role.name || ''));
const shouldShowAttendanceStatus = computed(() => {
  const isEarlyOrLate =
    form.value.rel_type === RelTypeEnum.EARLY ||
    form.value.rel_type === RelTypeEnum.LATE;
  const isUnpaidLeave = form.value.type_of_leave === LeaveOfType.UNPAID_LEAVE;
  return isNonEmployee.value && (isEarlyOrLate || isUnpaidLeave);
});

onMounted(() => {
  populateForm();
});
</script>

<template>
  <ion-page>
    <div :class="cn(
      'scroll-container mb-7 flex h-full flex-col items-center gap-y-7 p-4',
      isLoading ? 'justify-center' : 'justify-start',
    )
      ">
      <!-- Loading skeleton -->
      <div v-if="isLoading" class="w-full space-y-6">
        <div>
          <Skeleton class="h-4 w-32 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>

        <div>
          <Skeleton class="h-4 w-48 mb-3" />
          <Skeleton class="h-20 w-full rounded-lg" />
        </div>

        <div>
          <Skeleton class="h-4 w-28 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>

        <div>
          <Skeleton class="h-4 w-40 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>

        <div>
          <Skeleton class="h-4 w-20 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>

        <div>
          <Skeleton class="h-4 w-28 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-28 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>

        <div>
          <Skeleton class="h-4 w-32 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-32 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>

        <div>
          <Skeleton class="h-4 w-32 mb-2" />
          <Skeleton class="h-20 w-full" />
        </div>

        <div class="flex gap-x-4">
          <Skeleton class="h-12 w-full" />
          <Skeleton class="h-12 w-full" />
        </div>
      </div>

      <div v-else class="w-full">
        <n-form ref="formRef" label-placement="top" :model="form" :rules="rules" class="w-full">
          <n-form-item :label="t('leaves.new_request.subject')" path="subject" required>
            <n-input v-model:value="form.subject" :placeholder="t('leaves.new_request.subject_placeholder')" />
          </n-form-item>

          <!-- Display registered employees if this is a group leave -->
          <div v-if="!isSelectGroup && groupMembers.length > 0" class="mb-4">
            <Label class="mb-3 block font-medium">{{ t('leaves.new_request.staff') }} ({{ groupMembers.length
              }})</Label>

            <!-- Table Layout -->
            <div class="border border-gray-200 rounded-lg overflow-hidden">
              <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                <div class="grid grid-cols-2 gap-4 text-xs font-medium text-gray-700">
                  <div>Nhân viên</div>
                  <div>Mã NV</div>
                </div>
              </div>
              <div class="divide-y divide-gray-200">
                <div v-for="member in groupMembers" :key="member.id" class="px-4 py-3 hover:bg-gray-50">
                  <div class="grid grid-cols-2 gap-4 items-center">
                    <div class="text-sm font-medium text-gray-900">{{ member.full_name }}</div>
                    <div class="text-sm text-gray-500">{{ member.staff_identifi || 'N/A' }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Option to reselect employees for group leave -->
          <div v-if="isGroupLeave" :class="cn(!isSelectGroup && 'mb-4')">
            <n-checkbox v-model:checked="isSelectGroup" class="text-sm">
              {{ t('leaves.new_request.staff_placeholder') }}
            </n-checkbox>
          </div>
          <n-form-item v-if="isSelectGroup" path="group_staff_ids">
            <n-select v-model:value="form.employee_ids" :options="staffOptions"
              :placeholder="t('leaves.new_request.staff_placeholder')" multiple filterable />
          </n-form-item>

          <n-form-item :label="t('leaves.new_request.rel_type')" path="rel_type">
            <n-select v-model:value="form.rel_type" :options="relTypeOptions"
              :placeholder="t('leaves.new_request.rel_type_placeholder')" :disabled="true" />
          </n-form-item>

          <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.type_of_leave')" path="type_of_leave">
            <n-select v-model:value="form.type_of_leave" :options="leaveTypeOptions"
              :placeholder="t('leaves.new_request.type_of_leave_placeholder')" />
          </n-form-item>

          <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.days')" path="number_of_leaving_day" required>
            <n-input-number v-model:value="form.number_of_leaving_day"
              :placeholder="t('leaves.new_request.days_placeholder')" :min="0" :step="0.5" :default-value="0" />
          </n-form-item>

          <div v-if="isLeaveType" class="mb-4 space-y-1 font-semibold">
            <p>
              {{ t('leaves.new_request.total_days_off') }}: {{ totalDaysOff }}
            </p>
            <p>
              {{ t('leaves.new_request.remaining_days') }}: {{ remainDays }}
            </p>
          </div>

          <n-form-item :label="t('leaves.new_request.start_date')" path="start_time" required>
            <n-date-picker v-model:value="form.start_time" type="datetime" :format="dateFormats.datetime.display"
              :placeholder="dateFormats.datetime.placeholder" clearable class="w-full" />
          </n-form-item>

          <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.end_date')" path="end_time" required>
            <n-date-picker v-model:value="form.end_time" type="datetime" :format="dateFormats.datetime.display"
              :placeholder="dateFormats.datetime.placeholder" clearable class="w-full" />
          </n-form-item>

          <n-form-item :label="t('leaves.new_request.follower')" path="follower_id">
            <n-select filterable v-model:value="form.follower_id" :options="staffManagerOptions"
              :placeholder="t('leaves.new_request.follower_placeholder')" />
          </n-form-item>

          <n-form-item :label="t('leaves.new_request.approver')" path="approver_id">
            <n-select filterable v-model:value="form.approver_id" :options="staffApproveOptions"
              :placeholder="t('leaves.new_request.approver_placeholder')" />
          </n-form-item>

          <n-form-item v-if="shouldShowAttendanceStatus" :label="t('leaves.new_request.attendance_status')"
            path="is_deducted_attendance">
            <n-select v-model:value="form.is_deducted_attendance" :options="attendanceStatusOptions" :placeholder="t('leaves.new_request.attendance_status_placeholder')
              " />
          </n-form-item>

          <n-form-item :label="t('leaves.new_request.reason')" path="reason">
            <n-input v-model:value="form.reason" type="textarea"
              :placeholder="t('leaves.new_request.reason_placeholder')" />
          </n-form-item>

          <n-form-item v-if="form.type_of_leave == LeaveOfType.SICK_LEAVE" :label="t('common.choose_file')">
            <Input type="file" />
          </n-form-item>

          <div class="flex items-center gap-x-4">
            <Button type="button" variant="outline" size="lg" class="flex-1" @click="handleCancel">
              {{ t('common.cancel') }}
            </Button>
            <Button type="submit" size="lg" class="flex-1" @click="handleSubmit"
              :disabled="isSubmitDisabled || updateMutation.isPending.value" :loading="updateMutation.isPending.value">
              {{ t('leaves.edit_request.update') }}
            </Button>
          </div>
        </n-form>
      </div>
    </div>
  </ion-page>
</template>

<style scoped>
.n-form-item-label::after {
  content: '*';
  color: red;
  margin-left: 4px;
}

.n-date-picker {
  width: 100%;
}

.n-card {
  margin-bottom: 1rem;
}
</style>
