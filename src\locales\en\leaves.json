{"leaves": {"title": "Leaves & Holidays", "filter": {"title": "Filter", "leave_type": "Leave Type", "leave_status": "Leave Status", "start_date": "Start Date", "end_date": "End Date", "all_types": "All Types", "all_status": "All Status", "clear": "Clear", "apply": "Apply Filter", "status": "Status", "approver": "Approver", "select_date": "Select Date"}, "statistics": {"total": "Total", "approved": "Approved", "pending": "Pending", "rejected": "Rejected"}, "upcoming_holiday": {"title": "Upcoming Holidays", "no_holidays": "No upcoming holidays"}, "recent_leave": {"title": "Recent Leave Requests", "view_all": "View All", "no_requests": "No recent leave requests"}, "balance": {"title": "Leave Balance", "annual": "Annual Leave", "sick": "Sick Leave", "unpaid": "Unpaid Leave", "total": "Total", "used": "Used", "remaining": "Remaining", "new_request": "New Leave Request"}, "card": {"team_leave": "Team Leave", "view_details": "View Details", "cancel": "Cancel"}, "detail": {"title": "Leave Details", "date_information": "Date Information", "team_members": "Team Members", "start_date": "Start Date", "end_date": "End Date", "duration": "Duration", "days": "{count} day | {count} days", "reason": "Reason", "attachment": "Attachment", "attachment_file": "Attached File", "download": "Download", "close": "Close", "edit": "Edit", "timeline": {"title": "Timeline", "submitted": "Request Submitted", "approved": "Approved by Manager", "rejected": "Rejected by Manager"}}, "empty": {"title": "No Leave Requests", "description": "You haven't made any leave requests yet", "create_new": "Create New Request"}, "search": {"placeholder": "Search by leave type, status..."}, "history": {"title": "Leave History", "no_history": "No leave history available"}, "holiday": {"break_time": "Break time:", "days_remaining": "in {days} days", "no_upcoming_holidays": "No upcoming holidays"}, "common": {"days": "days", "count_days": "{count} days", "count_day": "{count} day", "used": "used", "not_applicable": "N/A", "clear_filter": "Clear Filter"}, "status": {"ALL": "All", "PENDING": "Pending", "APPROVED": "Approved", "REJECTED": "Rejected", "CANCELED": "Canceled"}, "rel_type": {"ALL": "All", "LEAVE": "Leave", "LATE": "Late", "EARLY": "Early Leave"}, "leave_type": {"ALL": "All Leave Types", "SICK_LEAVE": "Sick Leave", "MATERNITY_LEAVE": "Maternity Leave", "UNPAID_LEAVE": "Unpaid Leave", "ANNUAL_LEAVE": "Annual Leave"}, "attendance_status": {"deducted": "Deducted", "not_deducted": "Not Deducted"}, "new_request": {"title": "New Leave Request", "subject": "Subject", "subject_placeholder": "Enter leave request subject", "register_for_employee": "Register for employee", "staff": "Staff", "staff_placeholder": "Select staff", "rel_type": "Request Type", "rel_type_placeholder": "Select request type", "type_of_leave": "Leave Type", "type_of_leave_placeholder": "Select leave type", "days": "Number of Days", "days_placeholder": "Enter number of days", "total_days_off": "Total days off", "remaining_days": "Remaining days", "start_date": "Start Date", "end_date": "End Date", "follower": "Follower", "follower_placeholder": "Select follower", "approver": "Approver", "approver_placeholder": "Select approver", "attendance_status": "Attendance Status", "attendance_status_placeholder": "Select attendance status", "reason": "Reason", "reason_placeholder": "Enter your reason for leave", "attached_file": "Attached file", "submit": "Submit", "session": "Session", "session_placeholder": "Select session", "morning": "Morning (AM)", "afternoon": "Afternoon (PM)", "full_day": "Full Day", "calculated_days": "Calculated days", "working_days": "working days", "excluding_weekends": "excluding weekends"}, "edit_request": {"title": "Edit Leave Request", "update": "Update Request", "registered_member": "Registered Member"}, "validation": {"subject_required": "Subject is required", "days_required": "Number of days is required and must be at least 0.5", "start_date_required": "Start date is required", "end_date_required": "End date is required", "reason_required": "Reason is required", "approver_required": "Approver is required", "staff_required": "Staff is required", "type_of_leave_required": "Leave type is required"}, "management_messages": {"approve_success": "Approve successfully", "rejecte_success": "<PERSON><PERSON><PERSON> successfully", "cancele_success": "Cancele successfully"}}}