<!-- eslint-disable no-unused-vars -->
<script setup lang="ts">
import { Button } from '@/components/ui/button';
import type { Notification } from '@/interfaces/common';

import { Bell } from 'lucide-vue-next';
import { computed } from 'vue';
import NotificationItem from './NotificationItem.vue';
import { useI18n } from 'vue-i18n';
import { formatDateLocalized } from '@/utils/format';
import { useLocale } from '@/composables/useLocale';
const { t } = useI18n();

const { locale } = useLocale();
const props = defineProps<{
  notifications: Notification[];
}>();

const emit = defineEmits<{
  (e: 'markAsRead', id: string): void;
  (e: 'remove', id: string): void;
  (e: 'markAllAsRead'): void;
  (e: 'clearAll'): void;
}>();

const sortedNotifications = computed(() => {
  return [...props.notifications].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
  );
});

const groupedNotifications = computed(() => {
  const groups: Record<string, Notification[]> = {};

  sortedNotifications.value.forEach(notification => {
    const dateKey = new Date(notification.date).toDateString();
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(notification);
  });

  return groups;
});

const unreadCount = computed(() => {
  return props.notifications.filter((n) => n.is_read === 0).length;
});

const handleMarkAsRead = (id: string) => {
  emit('markAsRead', id);
};

const handleRemove = (id: string) => {
  emit('remove', id);
};

const handleMarkAllAsRead = () => {
  emit('markAllAsRead');
};

const handleClearAll = () => {
  emit('clearAll');
};

</script>

<template>
  <div class="mb-6 flex flex-col items-start justify-between sm:flex-row sm:items-center">
    <div class="flex gap-2">
      <Button variant="outline" size="sm" @click="handleMarkAllAsRead" :disabled="unreadCount === 0">
        {{ t('notification.mask_all_as_read') }}
      </Button>
      <Button variant="outline-destructive" size="sm" @click="handleClearAll" :disabled="notifications.length === 0">
        {{ t('notification.clear_all') }}
      </Button>
    </div>
  </div>



  <div v-if="sortedNotifications.length > 0">
    <div v-for="(notifications, group) in groupedNotifications" :key="group">
      <h4 class="mb-3 text-sm font-medium text-gray-500">{{ formatDateLocalized(group, locale) }}</h4>
      <NotificationItem v-for="notification in notifications" :key="notification.id" :notification="notification"
        @mark-as-read="handleMarkAsRead" @remove="handleRemove" />
    </div>
  </div>

  <div v-else class="py-12 text-center">
    <div class="mb-4 text-gray-400">
      <Bell class="mx-auto size-16" />
    </div>
    <h3 class="mb-2 text-lg font-semibold text-gray-900">{{ t('notification.no_notification') }}</h3>
    <p class="text-gray-600">
      You don't have any notifications at the moment.
    </p>
  </div>
</template>
