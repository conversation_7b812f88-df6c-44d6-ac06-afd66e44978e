<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { useDateFormats } from '@/composables/useDateFormats';
import { useLeaveForm } from '@/composables/useLeaveForm';
import { FormMode } from '@/enums';
import { LeaveOfType } from '@/enums/leave';
import { isEmployee } from '@/helpers/staff-helper';
import { cn } from '@/lib/utils';
import { IonPage } from '@ionic/vue';
import {
  NCheckbox,
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NSelect,
} from 'naive-ui';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const {
  form,
  formRef,
  hasEmployees,
  rules,
  relTypeOptions,
  leaveTypeOptions,
  staffOptions,
  staffManagerOptions,
  attendanceStatusOptions,
  remainDays,
  totalDaysOff,
  isSubmitDisabled,
  isLoading,
  RelTypeEnum,
  handleSubmit,
  user,
} = useLeaveForm(FormMode.CREATE);

const { dateFormats } = useDateFormats();

// Session options for individual date selection
const dateSessionOptions = [
  { label: t('leaves.new_request.full_day'), value: 'full_day' },
  { label: t('leaves.new_request.morning'), value: 'morning' },
  { label: t('leaves.new_request.afternoon'), value: 'afternoon' },
];

// Session selection for start and end dates
const startDateSession = ref('full_day');
const endDateSession = ref('full_day');

// Function to convert date to Vietnam timezone
const toVietnamDate = (date: number | Date): Date => {
  const d = new Date(date);
  // Convert to Vietnam timezone (UTC+7)
  const vietnamTime = new Date(d.getTime() + (7 * 60 * 60 * 1000));
  return new Date(vietnamTime.getFullYear(), vietnamTime.getMonth(), vietnamTime.getDate());
};

// Function to calculate leave days with session consideration
const calculateLeaveDays = (): number => {
  if (!form.value.start_time || !form.value.end_time || !isLeaveType.value) {
    return 0;
  }

  const startDate = toVietnamDate(form.value.start_time);
  const endDate = toVietnamDate(form.value.end_time);

  if (startDate > endDate) {
    return 0;
  }

  let totalDays = 0;
  const currentDate = new Date(startDate);

  // Check if it's the same day
  const isSameDay = startDate.getTime() === endDate.getTime();

  // Loop through each day from start to end (inclusive)
  while (currentDate <= endDate) {
    const dayOfWeek = currentDate.getDay();
    // Only count working days (Monday to Friday)
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      if (isSameDay) {
        // Same day - calculate based on sessions
        if (startDateSession.value === 'full_day' && endDateSession.value === 'full_day') {
          totalDays += 1;
        } else if (startDateSession.value === 'morning' && endDateSession.value === 'afternoon') {
          totalDays += 1;
        } else if (startDateSession.value === 'morning' && endDateSession.value === 'morning') {
          totalDays += 0.5;
        } else if (startDateSession.value === 'afternoon' && endDateSession.value === 'afternoon') {
          totalDays += 0.5;
        } else if (startDateSession.value === 'full_day' && endDateSession.value === 'morning') {
          totalDays += 0.5;
        } else if (startDateSession.value === 'afternoon' && endDateSession.value === 'full_day') {
          totalDays += 0.5;
        } else if (startDateSession.value === 'morning' && endDateSession.value === 'full_day') {
          totalDays += 1;
        } else if (startDateSession.value === 'full_day' && endDateSession.value === 'afternoon') {
          totalDays += 1;
        }
        break; // Exit loop since it's the same day
      } else if (currentDate.getTime() === startDate.getTime()) {
        // First day
        if (startDateSession.value === 'full_day') {
          totalDays += 1;
        } else {
          totalDays += 0.5;
        }
      } else if (currentDate.getTime() === endDate.getTime()) {
        // Last day
        if (endDateSession.value === 'full_day') {
          totalDays += 1;
        } else {
          totalDays += 0.5;
        }
      } else {
        // Middle days - always full day
        totalDays += 1;
      }
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return totalDays;
};

// Computed property for calculated leave days
const calculatedLeaveDays = computed(() => {
  return calculateLeaveDays();
});

// Computed properties for conditional rendering
const isLeaveType = computed(() => form.value.rel_type === RelTypeEnum.LEAVE);
const isNonEmployee = computed(() => !isEmployee(user?.value?.role.name || ''));
const shouldShowAttendanceStatus = computed(() => {
  const isEarlyOrLate =
    form.value.rel_type === RelTypeEnum.EARLY ||
    form.value.rel_type === RelTypeEnum.LATE;
  const isUnpaidLeave = form.value.type_of_leave === LeaveOfType.UNPAID_LEAVE;
  return isNonEmployee.value && (isEarlyOrLate || isUnpaidLeave);
});

// Function to set time based on session (Vietnam timezone)
const setTimeBySession = (date: number | null, sessionType: string, isStartTime: boolean = true) => {
  if (!date) return date;

  // Convert to Vietnam timezone
  const dateObj = toVietnamDate(date);

  switch (sessionType) {
    case 'morning':
      if (isStartTime) {
        dateObj.setHours(8, 0, 0, 0); // 8:00 AM
      } else {
        dateObj.setHours(12, 0, 0, 0); // 12:00 PM
      }
      break;
    case 'afternoon':
      if (isStartTime) {
        dateObj.setHours(13, 0, 0, 0); // 1:00 PM
      } else {
        dateObj.setHours(18, 0, 0, 0); // 6:00 PM
      }
      break;
    case 'full_day':
      if (isStartTime) {
        dateObj.setHours(8, 0, 0, 0); // 8:00 AM
      } else {
        dateObj.setHours(18, 0, 0, 0); // 6:00 PM
      }
      break;
  }

  return dateObj.getTime();
};


// Watch for session changes and update times
watch([startDateSession, endDateSession], () => {
  if (form.value.start_time) {
    form.value.start_time = setTimeBySession(form.value.start_time, startDateSession.value, true);
  }
  if (form.value.end_time) {
    form.value.end_time = setTimeBySession(form.value.end_time, endDateSession.value, false);
  }
});

// Watch for start_time changes and auto-set end_time if not set
watch(
  () => form.value.start_time,
  (newStartTime) => {
    if (newStartTime && isLeaveType.value && !form.value.end_time) {
      form.value.end_time = setTimeBySession(newStartTime, endDateSession.value, false);
    }
  },
);

// Watch for date changes and auto-calculate leave days
watch(
  [() => form.value.start_time, () => form.value.end_time, startDateSession, endDateSession],
  () => {
    if (isLeaveType.value && calculatedLeaveDays.value > 0) {
      form.value.number_of_leaving_day = calculatedLeaveDays.value;
    }
  },
  { immediate: true }
);
</script>

<template>
  <ion-page>
    <div :class="cn(
      'scroll-container mb-7 flex h-full flex-col items-center gap-y-7 p-4',
      isLoading ? 'justify-center' : 'justify-start',
    )
      ">
      <div v-if="isLoading" class="w-full space-y-6 animate-pulse">
        <div>
          <Skeleton class="h-4 w-32 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-48 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-40 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-28 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-28 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-32 mb-2" />
          <Skeleton class="h-20 w-full" />
        </div>
        <div class="flex gap-x-4">
          <Skeleton class="h-12 w-full" />
        </div>
      </div>
      <n-form v-else ref="formRef" label-placement="top" :model="form" :rules="rules" class="w-full">
        <n-form-item :label="t('leaves.new_request.subject')" path="subject" required>
          <n-input v-model:value="form.subject" :placeholder="t('leaves.new_request.subject_placeholder')" />
        </n-form-item>

        <n-checkbox v-if="isNonEmployee" v-model:checked="hasEmployees" size="small"
          :class="cn('w-full', hasEmployees ? 'mb-2' : 'mb-4')">
          {{ t('leaves.new_request.register_for_employee') }}
        </n-checkbox>

        <n-form-item v-if="isNonEmployee && hasEmployees" :label="t('leaves.new_request.staff')" path="employee_ids">
          <n-select filterable multiple v-model:value="form.employee_ids" :options="staffOptions"
            :placeholder="t('leaves.new_request.staff_placeholder')" />
        </n-form-item>

        <n-form-item :label="t('leaves.new_request.rel_type')" path="rel_type">
          <n-select v-model:value="form.rel_type" :options="relTypeOptions"
            :placeholder="t('leaves.new_request.rel_type_placeholder')" />
        </n-form-item>

        <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.type_of_leave')" path="type_of_leave">
          <n-select v-model:value="form.type_of_leave" :options="leaveTypeOptions"
            :placeholder="t('leaves.new_request.type_of_leave_placeholder')" />
        </n-form-item>

        <div v-if="isLeaveType" class="mb-4 space-y-1 font-semibold">
          <p>
            {{ t('leaves.new_request.total_days_off') }}: {{ totalDaysOff }}
          </p>
          <p>{{ t('leaves.new_request.remaining_days') }}: {{ remainDays }}</p>
        </div>

        <n-form-item :label="t('leaves.new_request.start_date')" path="start_time" required>
          <n-date-picker v-model:value="form.start_time" :format="dateFormats.date.display"
            :placeholder="dateFormats.date.placeholder" clearable class="w-full" />
        </n-form-item>

        <!-- Start Date Session Selection -->
        <n-form-item v-if="isLeaveType && form.start_time" :label="t('leaves.new_request.start_date_session')"
          path="start_date_session">
          <n-select v-model:value="startDateSession" :options="dateSessionOptions"
            :placeholder="t('leaves.new_request.session_placeholder')" />
        </n-form-item>

        <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.end_date')" path="end_time" required>
          <n-date-picker v-model:value="form.end_time" :format="dateFormats.date.display"
            :placeholder="dateFormats.date.placeholder" clearable class="w-full" />
        </n-form-item>

        <!-- End Date Session Selection -->
        <n-form-item v-if="isLeaveType && form.end_time" :label="t('leaves.new_request.end_date_session')"
          path="end_date_session">
          <n-select v-model:value="endDateSession" :options="dateSessionOptions"
            :placeholder="t('leaves.new_request.session_placeholder')" />
        </n-form-item>

        <!-- Display calculated leave days -->
        <div v-if="isLeaveType && form.start_time && form.end_time" class="mb-4 rounded bg-green-50 p-3">
          <p class="text-sm text-green-700">
            <span class="font-medium">{{ t('leaves.new_request.calculated_days') }}:</span>
            {{ calculatedLeaveDays }} {{ t('leaves.new_request.working_days') }}
            <span class="text-xs">({{ t('leaves.new_request.excluding_weekends') }})</span>
          </p>
        </div>

        <n-form-item :label="t('leaves.new_request.follower')" path="follower_id">
          <n-select filterable v-model:value="form.follower_id" :options="staffManagerOptions"
            :placeholder="t('leaves.new_request.follower_placeholder')" />
        </n-form-item>

        <n-form-item :label="t('leaves.new_request.approver')" path="approver_id">
          <n-select filterable v-model:value="form.approver_id" :options="staffManagerOptions"
            :placeholder="t('leaves.new_request.approver_placeholder')" />
        </n-form-item>

        <n-form-item v-if="shouldShowAttendanceStatus" :label="t('leaves.new_request.attendance_status')"
          path="is_deducted_attendance">
          <n-select v-model:value="form.is_deducted_attendance" :options="attendanceStatusOptions"
            :placeholder="t('leaves.new_request.attendance_status_placeholder')" />
        </n-form-item>

        <n-form-item :label="t('leaves.new_request.reason')" path="reason">
          <n-input v-model:value="form.reason" type="textarea"
            :placeholder="t('leaves.new_request.reason_placeholder')" />
        </n-form-item>

        <n-form-item v-if="form.type_of_leave == LeaveOfType.SICK_LEAVE" :label="t('common.choose_file')">
          <Input type="file" />
        </n-form-item>

        <Button type="submit" size="lg" class="w-full" @click="handleSubmit" :disabled="isSubmitDisabled">
          {{ t('leaves.new_request.submit') }}
        </Button>
      </n-form>
    </div>
  </ion-page>
</template>

<style scoped>
.n-form-item-label::after {
  content: '*';
  color: red;
  margin-left: 4px;
}

.n-date-picker {
  width: 100%;
}

.n-checkbox {
  --n-label-font-weight: 500 !important;
}
</style>
