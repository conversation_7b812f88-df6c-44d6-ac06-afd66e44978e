<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useDateFormats } from '@/composables/useDateFormats';
import { useLeaveForm } from '@/composables/useLeaveForm';
import { FormMode } from '@/enums';
import { LeaveOfType } from '@/enums/leave';
import { isEmployee } from '@/helpers/staff-helper';
import { cn } from '@/lib/utils';
import { IonPage } from '@ionic/vue';

import {
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NCheckbox,
} from 'naive-ui';
import { computed, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';

interface Props {
  leaveId: number;
}

const isSelectGroup = ref(false);
const props = defineProps<Props>();
const route = useRoute();
const { t } = useI18n();

const leaveId = computed(() => props.leaveId || Number(route.params.id));

const {
  form,
  formRef,
  rules,
  relTypeOptions,
  leaveTypeOptions,
  staffManagerOptions,
  staffOptions,
  staffApproveOptions,
  attendanceStatusOptions,
  remainDays,
  totalDaysOff,
  isSubmitDisabled,
  isLoading,
  groupMembers,
  isGroupLeave,
  RelTypeEnum,
  handleSubmit,
  handleCancel,
  populateForm,
  updateMutation,
  user,
} = useLeaveForm(FormMode.UPDATE, leaveId.value);
const { dateFormats } = useDateFormats();

// Session options for individual date selection
const dateSessionOptions = [
  { label: t('leaves.new_request.full_day'), value: 'full_day' },
  { label: t('leaves.new_request.morning'), value: 'morning' },
  { label: t('leaves.new_request.afternoon'), value: 'afternoon' },
];

// Session selection for start and end dates
const startDateSession = ref('full_day');
const endDateSession = ref('full_day');

// Function to convert date to Vietnam timezone
const toVietnamDate = (date: number | Date): Date => {
  const d = new Date(date);
  // Convert to Vietnam timezone (UTC+7)
  const vietnamTime = new Date(d.getTime() + (7 * 60 * 60 * 1000));
  return new Date(vietnamTime.getFullYear(), vietnamTime.getMonth(), vietnamTime.getDate());
};

// Function to calculate leave days with session consideration
const calculateLeaveDays = (): number => {
  if (!form.value.start_time || !form.value.end_time || !isLeaveType.value) {
    return 0;
  }

  const startDate = toVietnamDate(form.value.start_time);
  const endDate = toVietnamDate(form.value.end_time);

  if (startDate > endDate) {
    return 0;
  }

  let totalDays = 0;
  const currentDate = new Date(startDate);

  // Check if it's the same day
  const isSameDay = startDate.getTime() === endDate.getTime();

  // Loop through each day from start to end (inclusive)
  while (currentDate <= endDate) {
    const dayOfWeek = currentDate.getDay();
    // Only count working days (Monday to Friday)
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      if (isSameDay) {
        // Same day - calculate based on sessions
        if (startDateSession.value === 'full_day' && endDateSession.value === 'full_day') {
          totalDays += 1;
        } else if (startDateSession.value === 'morning' && endDateSession.value === 'afternoon') {
          totalDays += 1;
        } else if (startDateSession.value === 'morning' && endDateSession.value === 'morning') {
          totalDays += 0.5;
        } else if (startDateSession.value === 'afternoon' && endDateSession.value === 'afternoon') {
          totalDays += 0.5;
        } else if (startDateSession.value === 'full_day' && endDateSession.value === 'morning') {
          totalDays += 0.5;
        } else if (startDateSession.value === 'afternoon' && endDateSession.value === 'full_day') {
          totalDays += 0.5;
        } else if (startDateSession.value === 'morning' && endDateSession.value === 'full_day') {
          totalDays += 1;
        } else if (startDateSession.value === 'full_day' && endDateSession.value === 'afternoon') {
          totalDays += 1;
        }
        break; // Exit loop since it's the same day
      } else if (currentDate.getTime() === startDate.getTime()) {
        // First day
        if (startDateSession.value === 'full_day') {
          totalDays += 1;
        } else {
          totalDays += 0.5;
        }
      } else if (currentDate.getTime() === endDate.getTime()) {
        // Last day
        if (endDateSession.value === 'full_day') {
          totalDays += 1;
        } else {
          totalDays += 0.5;
        }
      } else {
        // Middle days - always full day
        totalDays += 1;
      }
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return totalDays;
};

// Computed property for calculated leave days
const calculatedLeaveDays = computed(() => {
  return calculateLeaveDays();
});

// Computed property to check if start and end dates are the same
const isSameDateSelected = computed(() => {
  if (!form.value.start_time || !form.value.end_time) {
    return false;
  }

  const startDate = toVietnamDate(form.value.start_time);
  const endDate = toVietnamDate(form.value.end_time);

  return startDate.getTime() === endDate.getTime();
});

// Computed properties for conditional rendering
const isLeaveType = computed(() => form.value.rel_type === RelTypeEnum.LEAVE);
const isNonEmployee = computed(() => !isEmployee(user?.value?.role.name || ''));
const shouldShowAttendanceStatus = computed(() => {
  const isEarlyOrLate =
    form.value.rel_type === RelTypeEnum.EARLY ||
    form.value.rel_type === RelTypeEnum.LATE;
  const isUnpaidLeave = form.value.type_of_leave === LeaveOfType.UNPAID_LEAVE;
  return isNonEmployee.value && (isEarlyOrLate || isUnpaidLeave);
});

// Watch for date and session changes and auto-calculate leave days
watch(
  [() => form.value.start_time, () => form.value.end_time, startDateSession, endDateSession],
  () => {
    if (isLeaveType.value && form.value.start_time && form.value.end_time) {
      const calculatedDays = calculateLeaveDays();
      if (calculatedDays > 0) {
        form.value.number_of_leaving_day = calculatedDays;
      }
    }
  },
  { immediate: true }
);

onMounted(() => {
  populateForm();
});
</script>

<template>
  <ion-page>
    <div :class="cn(
      'scroll-container mb-7 flex h-full flex-col items-center gap-y-7 p-4',
      isLoading ? 'justify-center' : 'justify-start',
    )
      ">
      <!-- Loading skeleton -->
      <div v-if="isLoading" class="w-full space-y-6">
        <div>
          <Skeleton class="h-4 w-32 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>

        <div>
          <Skeleton class="h-4 w-48 mb-3" />
          <Skeleton class="h-20 w-full rounded-lg" />
        </div>

        <div>
          <Skeleton class="h-4 w-28 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>

        <div>
          <Skeleton class="h-4 w-40 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>

        <div>
          <Skeleton class="h-4 w-20 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>

        <div>
          <Skeleton class="h-4 w-28 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-28 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>

        <div>
          <Skeleton class="h-4 w-32 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>
        <div>
          <Skeleton class="h-4 w-32 mb-2" />
          <Skeleton class="h-10 w-full" />
        </div>

        <div>
          <Skeleton class="h-4 w-32 mb-2" />
          <Skeleton class="h-20 w-full" />
        </div>

        <div class="flex gap-x-4">
          <Skeleton class="h-12 w-full" />
          <Skeleton class="h-12 w-full" />
        </div>
      </div>

      <div v-else class="w-full">
        <n-form ref="formRef" label-placement="top" :model="form" :rules="rules" class="w-full">
          <n-form-item :label="t('leaves.new_request.subject')" path="subject" required>
            <n-input v-model:value="form.subject" :placeholder="t('leaves.new_request.subject_placeholder')" />
          </n-form-item>

          <!-- Display registered employees if this is a group leave -->
          <div v-if="!isSelectGroup && groupMembers.length > 0" class="mb-4">
            <Label class="mb-3 block font-medium">{{ t('leaves.new_request.staff') }} ({{ groupMembers.length
            }})</Label>

            <!-- Table Layout -->
            <div class="border border-gray-200 rounded-lg overflow-hidden">
              <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                <div class="grid grid-cols-2 gap-4 text-xs font-medium text-gray-700">
                  <div>Nhân viên</div>
                  <div>Mã NV</div>
                </div>
              </div>
              <div class="divide-y divide-gray-200">
                <div v-for="member in groupMembers" :key="member.id" class="px-4 py-3 hover:bg-gray-50">
                  <div class="grid grid-cols-2 gap-4 items-center">
                    <div class="text-sm font-medium text-gray-900">{{ member.full_name }}</div>
                    <div class="text-sm text-gray-500">{{ member.staff_identifi || 'N/A' }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Option to reselect employees for group leave -->
          <div v-if="isGroupLeave" :class="cn(!isSelectGroup && 'mb-4')">
            <n-checkbox v-model:checked="isSelectGroup" class="text-sm">
              {{ t('leaves.new_request.staff_placeholder') }}
            </n-checkbox>
          </div>
          <n-form-item v-if="isSelectGroup" path="group_staff_ids">
            <n-select v-model:value="form.employee_ids" :options="staffOptions"
              :placeholder="t('leaves.new_request.staff_placeholder')" multiple filterable />
          </n-form-item>

          <n-form-item :label="t('leaves.new_request.rel_type')" path="rel_type">
            <n-select v-model:value="form.rel_type" :options="relTypeOptions"
              :placeholder="t('leaves.new_request.rel_type_placeholder')" :disabled="true" />
          </n-form-item>

          <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.type_of_leave')" path="type_of_leave">
            <n-select v-model:value="form.type_of_leave" :options="leaveTypeOptions"
              :placeholder="t('leaves.new_request.type_of_leave_placeholder')" />
          </n-form-item>

          <div v-if="isLeaveType" class="mb-4 space-y-1 font-semibold">
            <p>
              {{ t('leaves.new_request.total_days_off') }}: {{ totalDaysOff }}
            </p>
            <p>
              {{ t('leaves.new_request.remaining_days') }}: {{ remainDays }}
            </p>
          </div>

          <n-form-item :label="t('leaves.new_request.start_date')" path="start_time" required>
            <n-date-picker v-model:value="form.start_time" :format="dateFormats.date.display"
              :placeholder="dateFormats.date.placeholder" clearable class="w-full" />
          </n-form-item>

          <!-- Start Date Session Selection -->
          <n-form-item v-if="isLeaveType && form.start_time" :label="t('leaves.new_request.start_date_session')"
            path="start_date_session">
            <n-select v-model:value="startDateSession" :options="dateSessionOptions"
              :placeholder="t('leaves.new_request.session_placeholder')" />
          </n-form-item>

          <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.end_date')" path="end_time" required>
            <n-date-picker v-model:value="form.end_time" :format="dateFormats.date.display"
              :placeholder="dateFormats.date.placeholder" clearable class="w-full" />
          </n-form-item>

          <!-- End Date Session Selection -->
          <n-form-item v-if="isLeaveType && form.end_time" :label="t('leaves.new_request.end_date_session')"
            path="end_date_session">
            <n-select v-model:value="endDateSession" :options="dateSessionOptions"
              :placeholder="t('leaves.new_request.session_placeholder')" :disabled="isSameDateSelected" />
          </n-form-item>

          <!-- Display calculated leave days -->
          <div v-if="isLeaveType && form.start_time && form.end_time" class="mb-4 rounded bg-green-50 p-3">
            <p class="text-sm text-green-700">
              <span class="font-medium">{{ t('leaves.new_request.calculated_days') }}:</span>
              {{ calculatedLeaveDays }} {{ t('leaves.new_request.working_days') }}
              <span class="text-xs">({{ t('leaves.new_request.excluding_weekends') }})</span>
            </p>
          </div>

          <n-form-item :label="t('leaves.new_request.follower')" path="follower_id">
            <n-select filterable v-model:value="form.follower_id" :options="staffManagerOptions"
              :placeholder="t('leaves.new_request.follower_placeholder')" />
          </n-form-item>

          <n-form-item :label="t('leaves.new_request.approver')" path="approver_id">
            <n-select filterable v-model:value="form.approver_id" :options="staffApproveOptions"
              :placeholder="t('leaves.new_request.approver_placeholder')" />
          </n-form-item>

          <n-form-item v-if="shouldShowAttendanceStatus" :label="t('leaves.new_request.attendance_status')"
            path="is_deducted_attendance">
            <n-select v-model:value="form.is_deducted_attendance" :options="attendanceStatusOptions" :placeholder="t('leaves.new_request.attendance_status_placeholder')
              " />
          </n-form-item>

          <n-form-item :label="t('leaves.new_request.reason')" path="reason">
            <n-input v-model:value="form.reason" type="textarea"
              :placeholder="t('leaves.new_request.reason_placeholder')" />
          </n-form-item>

          <n-form-item v-if="form.type_of_leave == LeaveOfType.SICK_LEAVE" :label="t('common.choose_file')">
            <Input type="file" />
          </n-form-item>

          <div class="flex items-center gap-x-4">
            <Button type="button" variant="outline" size="lg" class="flex-1" @click="handleCancel">
              {{ t('common.cancel') }}
            </Button>
            <Button type="submit" size="lg" class="flex-1" @click="handleSubmit"
              :disabled="isSubmitDisabled || updateMutation.isPending.value" :loading="updateMutation.isPending.value">
              {{ t('leaves.edit_request.update') }}
            </Button>
          </div>
        </n-form>
      </div>
    </div>
  </ion-page>
</template>

<style scoped>
.n-form-item-label::after {
  content: '*';
  color: red;
  margin-left: 4px;
}

.n-date-picker {
  width: 100%;
}

.n-card {
  margin-bottom: 1rem;
}
</style>
