import { computed } from 'vue';
import { getNotifications } from '@/services/notifications.service';
import { useQuery } from '@tanstack/vue-query';

export function useNotifications() {
  const notificationQuery = useQuery({
    queryKey: ['notifications'],
    queryFn: getNotifications,
  });

  // const removeNotification = async (id: number) => {
  //   await removeNotification(id);
  //   notificationQuery.refetch();
  // };

  const data = computed(() => notificationQuery.data.value?.data?.notifications || []);
  const isLoading = computed(() => notificationQuery.isLoading.value);
  const error = computed(() => notificationQuery.error.value);
  const refetch = notificationQuery.refetch;

  return {
    data,
    isLoading,
    error,
    refetch,
    notificationQuery,
  };
}
