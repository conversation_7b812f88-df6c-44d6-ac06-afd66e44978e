{"leaves": {"title": "休暇と祝日", "filter": {"title": "フィルター", "leave_type": "休暇種類", "leave_status": "ステータス", "start_date": "開始日", "end_date": "終了日", "all_types": "すべての種類", "all_status": "すべてのステータス", "clear": "クリア", "apply": "適用", "status": "ステータス", "approver": "承認者", "select_date": "日付を選択"}, "statistics": {"title": "休暇統計", "total": "合計", "approved": "承認済み", "pending": "保留中", "rejected": "却下"}, "upcoming_holiday": {"title": "今後の祝日", "no_holidays": "予定されている祝日はありません"}, "recent_leave": {"title": "最近の休暇申請", "view_all": "すべて表示", "no_requests": "最近の休暇申請はありません"}, "balance": {"title": "休暇残高", "annual": "年次有給休暇", "sick": "病気休暇", "unpaid": "無給休暇", "total": "合計", "used": "使用済み", "remaining": "残り", "new_request": "新規休暇申請"}, "card": {"team_leave": "チーム休暇", "view_details": "詳細を表示", "cancel": "キャンセル"}, "detail": {"title": "休暇詳細", "date_information": "日付情報", "team_members": "チームメンバー", "start_date": "開始日", "end_date": "終了日", "duration": "期間", "days": "{count}日", "reason": "理由", "attachment": "添付ファイル", "attachment_file": "添付されたファイル", "download": "ダウンロード", "close": "閉じる", "edit": "編集", "timeline": {"title": "タイムライン", "submitted": "申請済み", "approved": "マネージャーによって承認", "rejected": "マネージャーによって却下"}}, "empty": {"title": "休暇申請なし", "description": "まだ休暇申請がありません", "create_new": "新規申請"}, "search": {"placeholder": "休暇種類、ステータスで検索..."}, "history": {"title": "休暇履歴", "no_history": "休暇履歴はありません"}, "holiday": {"break_time": "休憩時間", "days_remaining": "{days} 日後", "no_upcoming_holidays": "今後の祝日はありません"}, "common": {"days": "日", "count_days": "{count} 日", "count_day": "{count} 日", "used": "使用済み", "not_applicable": "N/A", "clear_filter": "フィルターをクリア"}, "status": {"ALL": "すべて", "PENDING": "保留中", "APPROVED": "承認済み", "REJECTED": "却下", "CANCELED": "キャンセル"}, "rel_type": {"ALL": "すべての申請", "LEAVE": "休暇", "LATE": "遅刻", "EARLY": "早退"}, "leave_type": {"ALL": "すべての休暇種類", "SICK_LEAVE": "病気休暇", "MATERNITY_LEAVE": "産休", "UNPAID_LEAVE": "無給休暇", "ANNUAL_LEAVE": "年次有給休暇"}, "attendance_status": {"deducted": "控除済み", "not_deducted": "控除なし"}, "new_request": {"title": "休暇申請を作成", "subject": "件名", "subject_placeholder": "休暇申請の件名を入力", "register_for_employee": "従業員のために登録", "staff": "スタッフ", "staff_placeholder": "スタッフを選択", "rel_type": "申請種類", "rel_type_placeholder": "申請種類を選択", "type_of_leave": "休暇種類", "type_of_leave_placeholder": "休暇種類を選択", "days": "日数", "days_placeholder": "日数を入力", "total_days_off": "総休暇日数", "remaining_days": "残り日数", "start_date": "開始日", "start_date_session": "開始日の時間帯", "end_date": "終了日", "end_date_session": "終了日の時間帯", "follower": "フォロワー", "follower_placeholder": "フォロワーを選択", "approver": "承認者", "approver_placeholder": "承認者を選択", "attendance_status": "勤怠ステータス", "attendance_status_placeholder": "勤怠ステータスを選択", "reason": "理由", "reason_placeholder": "休暇の理由を入力", "attached_file": "添付ファイル", "submit": "送信", "session": "時間帯", "session_placeholder": "時間帯を選択", "morning": "午前 (AM)", "afternoon": "午後 (PM)", "full_day": "終日", "calculated_days": "計算された日数", "working_days": "営業日", "excluding_weekends": "土日を除く"}, "edit_request": {"title": "休暇申請を編集", "update": "申請を更新", "registered_member": "登録済みメンバー"}, "validation": {"subject_required": "件名は必須です", "days_required": "日数は必須で、0.5以上である必要があります", "start_date_required": "開始日は必須です", "end_date_required": "終了日は必須です", "reason_required": "理由は必須です", "approver_required": "承認者は必須です", "staff_required": "スタッフは必須です", "type_of_leave_required": "休暇種類は必須です"}}}